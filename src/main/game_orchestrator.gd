class_name GameOrchestrator
extends Node

@export var hub_scene: PackedScene
@export var run_levels: Array[PackedScene]

var _scenes_container: Node
var _current_scene_instance: Node
var _current_level_index: int = -1

func initialize(scenes_container: Node) -> void:
	self._scenes_container = scenes_container

func start_run() -> void:
	_on_start_run()

func go_to_hub() -> void:
	go_to_scene(hub_scene)

func go_to_scene(scene_resource: PackedScene) -> void:
	await _remove_current_scene()
	_add_new_scene(scene_resource)

func _remove_current_scene() -> void:
	if not is_instance_valid(_current_scene_instance):
		return

	_disconnect_current_scene_signals()
	_current_scene_instance.queue_free()
	await _current_scene_instance.tree_exited

func _add_new_scene(scene_resource: PackedScene) -> void:
	var new_scene: Node = scene_resource.instantiate()
	_scenes_container.add_child(new_scene)
	_current_scene_instance = new_scene

	_connect_current_scene_signals()

func _connect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.connect(_on_level_completed)
		level_instance.level_failed.connect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.connect(_on_start_run)

func _disconnect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.disconnect(_on_level_completed)
		level_instance.level_failed.disconnect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.disconnect(_on_start_run)

func _on_start_run() -> void:
	_current_level_index = 0
	if not run_levels.is_empty():
		go_to_scene(run_levels[_current_level_index])
	else:
		push_error("Нет уровней для запуска!")
		go_to_hub()

func _on_level_completed() -> void:
	_current_level_index += 1
	if _current_level_index < run_levels.size():
		go_to_scene(run_levels[_current_level_index])
	else:
		go_to_hub()

func _on_level_failed() -> void:
	go_to_hub()
